# encoding:utf-8

"""
线程通信模块

提供GUI线程与后台服务线程之间的安全通信机制。
包括消息队列、消息类型定义和通信协议。
"""

import queue
import threading
import time
from enum import Enum
from typing import Any, Dict, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

from common.log import logger


class MessageType(Enum):
    """消息类型枚举"""
    # 服务控制消息
    SERVICE_START = "service_start"
    SERVICE_PAUSE = "service_pause"
    SERVICE_RESUME = "service_resume"
    SERVICE_STOP = "service_stop"
    
    # 状态更新消息
    STATUS_UPDATE = "status_update"
    LOG_MESSAGE = "log_message"
    CHAT_MESSAGE = "chat_message"
    ERROR_MESSAGE = "error_message"
    
    # 配置消息
    CONFIG_UPDATE = "config_update"
    CONFIG_RELOAD = "config_reload"
    
    # 统计信息消息
    STATS_UPDATE = "stats_update"
    
    # 系统消息
    HEARTBEAT = "heartbeat"
    SHUTDOWN = "shutdown"


@dataclass
class Message:
    """消息数据类"""
    type: MessageType
    data: Any = None
    timestamp: str = None
    source: str = None
    target: str = None
    correlation_id: str = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'type': self.type.value,
            'data': self.data,
            'timestamp': self.timestamp,
            'source': self.source,
            'target': self.target,
            'correlation_id': self.correlation_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """从字典创建消息"""
        return cls(
            type=MessageType(data['type']),
            data=data.get('data'),
            timestamp=data.get('timestamp'),
            source=data.get('source'),
            target=data.get('target'),
            correlation_id=data.get('correlation_id')
        )


class ThreadSafeQueue:
    """
    线程安全队列
    
    提供GUI线程与后台服务线程之间的安全通信机制。
    支持消息优先级、超时处理和异常处理。
    """
    
    def __init__(self, maxsize: int = 1000):
        """
        初始化线程安全队列
        
        Args:
            maxsize: 队列最大大小，0表示无限制
        """
        self._queue = queue.Queue(maxsize=maxsize)
        self._lock = threading.RLock()
        self._closed = False
        self._message_handlers = {}
        self._stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'messages_dropped': 0,
            'errors': 0
        }
        
        logger.debug(f"[ThreadSafeQueue] 初始化队列，最大大小: {maxsize}")
    
    def put(self, message: Message, timeout: Optional[float] = None) -> bool:
        """
        发送消息到队列
        
        Args:
            message: 要发送的消息
            timeout: 超时时间（秒），None表示阻塞等待
            
        Returns:
            bool: 是否成功发送
        """
        if self._closed:
            logger.warning("[ThreadSafeQueue] 队列已关闭，无法发送消息")
            return False
        
        try:
            with self._lock:
                self._queue.put(message, timeout=timeout)
                self._stats['messages_sent'] += 1
                
            logger.debug(f"[ThreadSafeQueue] 发送消息: {message.type.value}")
            return True
            
        except queue.Full:
            logger.warning(f"[ThreadSafeQueue] 队列已满，丢弃消息: {message.type.value}")
            self._stats['messages_dropped'] += 1
            return False
        except Exception as e:
            logger.error(f"[ThreadSafeQueue] 发送消息失败: {str(e)}")
            self._stats['errors'] += 1
            return False
    
    def get(self, timeout: Optional[float] = None) -> Optional[Message]:
        """
        从队列获取消息
        
        Args:
            timeout: 超时时间（秒），None表示阻塞等待
            
        Returns:
            Message: 获取的消息，超时或队列关闭时返回None
        """
        if self._closed:
            return None
        
        try:
            message = self._queue.get(timeout=timeout)
            with self._lock:
                self._stats['messages_received'] += 1
            
            logger.debug(f"[ThreadSafeQueue] 接收消息: {message.type.value}")
            return message
            
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"[ThreadSafeQueue] 接收消息失败: {str(e)}")
            self._stats['errors'] += 1
            return None
    
    def put_nowait(self, message: Message) -> bool:
        """
        非阻塞发送消息
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 是否成功发送
        """
        return self.put(message, timeout=0)
    
    def get_nowait(self) -> Optional[Message]:
        """
        非阻塞获取消息
        
        Returns:
            Message: 获取的消息，队列为空时返回None
        """
        return self.get(timeout=0)
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()
    
    def close(self):
        """关闭队列"""
        with self._lock:
            self._closed = True
            
            # 清空队列
            while not self._queue.empty():
                try:
                    self._queue.get_nowait()
                except queue.Empty:
                    break
        
        logger.info("[ThreadSafeQueue] 队列已关闭")
    
    def is_closed(self) -> bool:
        """检查队列是否已关闭"""
        return self._closed
    
    def get_stats(self) -> Dict[str, int]:
        """获取队列统计信息"""
        with self._lock:
            return self._stats.copy()
    
    def register_handler(self, message_type: MessageType, handler: Callable[[Message], None]):
        """
        注册消息处理器
        
        Args:
            message_type: 消息类型
            handler: 处理函数
        """
        with self._lock:
            if message_type not in self._message_handlers:
                self._message_handlers[message_type] = []
            self._message_handlers[message_type].append(handler)
        
        logger.debug(f"[ThreadSafeQueue] 注册消息处理器: {message_type.value}")
    
    def unregister_handler(self, message_type: MessageType, handler: Callable[[Message], None]):
        """
        注销消息处理器
        
        Args:
            message_type: 消息类型
            handler: 处理函数
        """
        with self._lock:
            if message_type in self._message_handlers:
                try:
                    self._message_handlers[message_type].remove(handler)
                    logger.debug(f"[ThreadSafeQueue] 注销消息处理器: {message_type.value}")
                except ValueError:
                    pass
    
    def process_message(self, message: Message):
        """
        处理消息（调用注册的处理器）
        
        Args:
            message: 要处理的消息
        """
        handlers = self._message_handlers.get(message.type, [])
        
        for handler in handlers[:]:  # 复制列表避免并发修改
            try:
                handler(message)
            except Exception as e:
                logger.error(f"[ThreadSafeQueue] 消息处理器错误: {str(e)}")
                self._stats['errors'] += 1


class MessageBus:
    """
    消息总线
    
    提供发布-订阅模式的消息通信机制。
    支持多个订阅者和消息过滤。
    """
    
    def __init__(self):
        """初始化消息总线"""
        self._subscribers = {}
        self._lock = threading.RLock()
        self._running = True
        
        logger.debug("[MessageBus] 消息总线初始化完成")
    
    def subscribe(self, message_type: MessageType, callback: Callable[[Message], None]):
        """
        订阅消息类型
        
        Args:
            message_type: 消息类型
            callback: 回调函数
        """
        with self._lock:
            if message_type not in self._subscribers:
                self._subscribers[message_type] = []
            self._subscribers[message_type].append(callback)
        
        logger.debug(f"[MessageBus] 订阅消息: {message_type.value}")
    
    def unsubscribe(self, message_type: MessageType, callback: Callable[[Message], None]):
        """
        取消订阅消息类型
        
        Args:
            message_type: 消息类型
            callback: 回调函数
        """
        with self._lock:
            if message_type in self._subscribers:
                try:
                    self._subscribers[message_type].remove(callback)
                    logger.debug(f"[MessageBus] 取消订阅消息: {message_type.value}")
                except ValueError:
                    pass
    
    def publish(self, message: Message):
        """
        发布消息
        
        Args:
            message: 要发布的消息
        """
        if not self._running:
            return
        
        subscribers = self._subscribers.get(message.type, [])
        
        for callback in subscribers[:]:  # 复制列表避免并发修改
            try:
                callback(message)
            except Exception as e:
                logger.error(f"[MessageBus] 消息订阅者回调错误: {str(e)}")
    
    def shutdown(self):
        """关闭消息总线"""
        with self._lock:
            self._running = False
            self._subscribers.clear()
        
        logger.info("[MessageBus] 消息总线已关闭")
