# encoding:utf-8

"""
GUI主界面管理器

负责创建和管理主界面，协调各个子组件，处理用户交互事件。
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional

from common.log import logger
from .state_manager import StateManager, ServiceStatus, GUIState
from .thread_communication import ThreadSafeQueue, MessageBus, Message, MessageType
from .service_controller import ServiceController
from .log_monitor import LogMonitor
from .message_recorder import MessageRecorder
from .config_editor import ConfigEditor
from .status_indicator import StatusIndicator


class GUIManager:
    """
    GUI主界面管理器
    
    负责创建和管理整个GUI界面，协调各个子组件的工作。
    """
    
    def __init__(self, root: tk.Tk):
        """
        初始化GUI管理器
        
        Args:
            root: Tkinter根窗口
        """
        self.root = root
        self.state_manager = StateManager()
        self.message_queue = ThreadSafeQueue()
        self.message_bus = MessageBus()
        
        # 子组件
        self.service_controller = ServiceController(self.message_queue)
        self.log_monitor = None  # 稍后在创建选项卡时初始化
        self.message_recorder = None  # 稍后在创建选项卡时初始化
        self.config_editor = None  # 稍后在创建选项卡时初始化
        self.status_indicator = StatusIndicator(self.root)
        
        # GUI组件
        self.main_frame = None
        self.toolbar_frame = None
        self.content_frame = None
        self.status_frame = None
        
        # 控制按钮
        self.start_button = None
        self.pause_button = None
        self.stop_button = None
        
        # 状态标签
        self.status_label = None
        
        # 初始化界面
        self._create_interface()
        self._setup_event_handlers()
        
        # 设置GUI状态为就绪
        self.state_manager.set_gui_state(GUIState.READY)
        
        logger.info("[GUIManager] GUI管理器初始化完成")
    
    def _create_interface(self):
        """创建主界面"""
        try:
            # 设置主题样式
            style = ttk.Style()
            style.theme_use('clam')  # 使用现代主题
            
            # 创建主框架
            self.main_frame = ttk.Frame(self.root)
            self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 创建工具栏
            self._create_toolbar()
            
            # 创建内容区域
            self._create_content_area()
            
            # 创建状态栏
            self._create_status_bar()
            
            logger.info("[GUIManager] 主界面创建完成")
            
        except Exception as e:
            logger.error(f"[GUIManager] 创建界面失败: {str(e)}")
            raise
    
    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 服务控制按钮组
        control_frame = ttk.LabelFrame(self.toolbar_frame, text="服务控制", padding=5)
        control_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # 启动按钮
        self.start_button = ttk.Button(
            control_frame,
            text="启动",
            command=self._on_start_clicked,
            width=8
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 暂停按钮
        self.pause_button = ttk.Button(
            control_frame,
            text="暂停",
            command=self._on_pause_clicked,
            width=8,
            state=tk.DISABLED
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 停止按钮
        self.stop_button = ttk.Button(
            control_frame,
            text="停止",
            command=self._on_stop_clicked,
            width=8,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT)
        
        # 其他工具按钮组
        tools_frame = ttk.LabelFrame(self.toolbar_frame, text="工具", padding=5)
        tools_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # 配置按钮
        config_button = ttk.Button(
            tools_frame,
            text="配置",
            command=self._on_config_clicked,
            width=8
        )
        config_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 日志按钮
        log_button = ttk.Button(
            tools_frame,
            text="日志",
            command=self._on_log_clicked,
            width=8
        )
        log_button.pack(side=tk.LEFT)
    
    def _create_content_area(self):
        """创建内容区域"""
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 概览选项卡
        self._create_overview_tab()
        
        # 日志选项卡
        self._create_log_tab()
        
        # 消息选项卡
        self._create_message_tab()
        
        # 配置选项卡
        self._create_config_tab()
    
    def _create_overview_tab(self):
        """创建概览选项卡"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="概览")
        
        # 状态信息区域
        status_info_frame = ttk.LabelFrame(overview_frame, text="状态信息", padding=10)
        status_info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 服务状态
        ttk.Label(status_info_frame, text="服务状态:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.service_status_label = ttk.Label(status_info_frame, text="已停止", foreground="red")
        self.service_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 运行时间
        ttk.Label(status_info_frame, text="运行时间:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.uptime_label = ttk.Label(status_info_frame, text="0秒")
        self.uptime_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 统计信息区域
        stats_frame = ttk.LabelFrame(overview_frame, text="统计信息", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 接收消息数
        ttk.Label(stats_frame, text="接收消息:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.received_count_label = ttk.Label(stats_frame, text="0")
        self.received_count_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 发送消息数
        ttk.Label(stats_frame, text="发送消息:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.sent_count_label = ttk.Label(stats_frame, text="0")
        self.sent_count_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 错误数
        ttk.Label(stats_frame, text="错误数量:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.error_count_label = ttk.Label(stats_frame, text="0")
        self.error_count_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
    
    def _create_log_tab(self):
        """创建日志选项卡"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="日志")

        # 创建日志监控器
        self.log_monitor = LogMonitor(log_frame)
    
    def _create_message_tab(self):
        """创建消息选项卡"""
        message_frame = ttk.Frame(self.notebook)
        self.notebook.add(message_frame, text="消息")

        # 创建消息记录器
        self.message_recorder = MessageRecorder(message_frame)
    
    def _create_config_tab(self):
        """创建配置选项卡"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="配置")

        # 创建配置编辑器
        self.config_editor = ConfigEditor(config_frame)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X)
        
        # 状态标签
        self.status_label = ttk.Label(
            self.status_frame,
            text="就绪",
            relief=tk.SUNKEN,
            anchor=tk.W,
            padding=(5, 2)
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 时间标签
        self.time_label = ttk.Label(
            self.status_frame,
            text="",
            relief=tk.SUNKEN,
            anchor=tk.E,
            padding=(5, 2),
            width=20
        )
        self.time_label.pack(side=tk.RIGHT)
        
        # 启动时间更新
        self._update_time()
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 监听状态变更
        self.state_manager.add_listener('service_status', self._on_service_status_changed)
        self.state_manager.add_listener('statistics', self._on_statistics_changed)
    
    def _on_start_clicked(self):
        """处理启动按钮点击"""
        logger.info("[GUIManager] 用户点击启动按钮")
        success = self.service_controller.start_service()
        if not success:
            messagebox.showerror("错误", "启动服务失败，请查看日志了解详情")

    def _on_pause_clicked(self):
        """处理暂停按钮点击"""
        logger.info("[GUIManager] 用户点击暂停按钮")
        current_status = self.service_controller.get_service_status()

        if current_status == ServiceStatus.RUNNING:
            success = self.service_controller.pause_service()
            if not success:
                messagebox.showerror("错误", "暂停服务失败，请查看日志了解详情")
        elif current_status == ServiceStatus.PAUSED:
            success = self.service_controller.resume_service()
            if success:
                self.pause_button.config(text="暂停")
            else:
                messagebox.showerror("错误", "恢复服务失败，请查看日志了解详情")

    def _on_stop_clicked(self):
        """处理停止按钮点击"""
        logger.info("[GUIManager] 用户点击停止按钮")
        success = self.service_controller.stop_service()
        if not success:
            messagebox.showerror("错误", "停止服务失败，请查看日志了解详情")
    
    def _on_config_clicked(self):
        """处理配置按钮点击"""
        logger.info("[GUIManager] 用户点击配置按钮")
        # 切换到配置选项卡
        self.notebook.select(3)  # 配置选项卡索引
    
    def _on_log_clicked(self):
        """处理日志按钮点击"""
        logger.info("[GUIManager] 用户点击日志按钮")
        # 切换到日志选项卡
        self.notebook.select(1)  # 日志选项卡索引
    
    def _on_service_status_changed(self, event_type: str, old_status: ServiceStatus, new_status: ServiceStatus):
        """处理服务状态变更"""
        # 在主线程中更新UI
        self.root.after(0, self._update_service_status_ui, new_status)
    
    def _on_statistics_changed(self, event_type: str, old_stats, new_stats):
        """处理统计信息变更"""
        # 在主线程中更新UI
        self.root.after(0, self._update_statistics_ui, new_stats)
    
    def _update_service_status_ui(self, status: ServiceStatus):
        """更新服务状态UI"""
        status_text = {
            ServiceStatus.STOPPED: ("已停止", "red"),
            ServiceStatus.STARTING: ("启动中", "orange"),
            ServiceStatus.RUNNING: ("运行中", "green"),
            ServiceStatus.PAUSING: ("暂停中", "orange"),
            ServiceStatus.PAUSED: ("已暂停", "blue"),
            ServiceStatus.STOPPING: ("停止中", "orange"),
            ServiceStatus.ERROR: ("错误", "red")
        }
        
        text, color = status_text.get(status, ("未知", "gray"))
        self.service_status_label.config(text=text, foreground=color)
        
        # 更新按钮状态
        if status == ServiceStatus.STOPPED:
            self.start_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)
        elif status == ServiceStatus.RUNNING:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)
        elif status == ServiceStatus.PAUSED:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(text="恢复", state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)
        else:
            # 过渡状态，禁用所有按钮
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)
    
    def _update_statistics_ui(self, stats):
        """更新统计信息UI"""
        self.received_count_label.config(text=str(stats.get('messages_received', 0)))
        self.sent_count_label.config(text=str(stats.get('messages_sent', 0)))
        self.error_count_label.config(text=str(stats.get('errors_count', 0)))
        
        # 更新运行时间
        uptime = stats.get('uptime_seconds', 0)
        if uptime > 0:
            hours = uptime // 3600
            minutes = (uptime % 3600) // 60
            seconds = uptime % 60
            uptime_text = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            uptime_text = "0秒"
        self.uptime_label.config(text=uptime_text)
    
    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        
        # 每秒更新一次
        self.root.after(1000, self._update_time)
    
    def on_closing(self) -> bool:
        """
        处理窗口关闭事件
        
        Returns:
            bool: 是否允许关闭
        """
        try:
            # 检查服务状态
            service_status = self.state_manager.get_service_status()
            if service_status in [ServiceStatus.RUNNING, ServiceStatus.STARTING]:
                result = messagebox.askyesnocancel(
                    "确认关闭",
                    "服务正在运行中，关闭GUI不会停止后台服务。\n\n"
                    "是否要停止服务并退出？\n"
                    "选择'是'停止服务并退出\n"
                    "选择'否'仅关闭GUI\n"
                    "选择'取消'继续运行"
                )
                
                if result is None:  # 取消
                    return False
                elif result:  # 是，停止服务
                    # TODO: 停止服务
                    pass
                # 否，仅关闭GUI，继续执行
            
            return True
            
        except Exception as e:
            logger.error(f"[GUIManager] 关闭处理错误: {str(e)}")
            return True
    
    def cleanup(self):
        """清理资源"""
        try:
            logger.info("[GUIManager] 开始清理GUI资源...")
            
            # 关闭消息队列和总线
            if self.message_queue:
                self.message_queue.close()
            
            if self.message_bus:
                self.message_bus.shutdown()
            
            # 清理子组件
            if self.service_controller:
                self.service_controller.cleanup()

            if self.log_monitor:
                self.log_monitor.cleanup()

            if self.message_recorder:
                self.message_recorder.cleanup()

            if self.config_editor:
                self.config_editor.cleanup()

            if self.status_indicator:
                self.status_indicator.cleanup()
            
            logger.info("[GUIManager] GUI资源清理完成")
            
        except Exception as e:
            logger.error(f"[GUIManager] 清理资源时发生错误: {str(e)}")
