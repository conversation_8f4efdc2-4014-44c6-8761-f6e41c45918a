# encoding:utf-8

"""
GUI基础架构测试程序
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        
        # 测试现有项目模块
        from common.log import logger
        print("✓ common.log 导入成功")
        
        from config import conf, load_config
        print("✓ config 导入成功")
        
        # 测试GUI模块
        from gui.state_manager import StateManager, ServiceStatus, GUIState
        print("✓ state_manager 导入成功")
        
        from gui.thread_communication import ThreadSafeQueue, MessageBus, Message, MessageType
        print("✓ thread_communication 导入成功")
        
        from gui.gui_manager import GUIManager
        print("✓ gui_manager 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {str(e)}")
        return False

def test_state_manager():
    """测试状态管理器"""
    try:
        print("\n测试状态管理器...")
        
        from gui.state_manager import StateManager, ServiceStatus
        
        # 创建状态管理器
        sm = StateManager()
        print("✓ 状态管理器创建成功")
        
        # 测试状态设置和获取
        sm.set_service_status(ServiceStatus.RUNNING)
        status = sm.get_service_status()
        assert status == ServiceStatus.RUNNING
        print("✓ 状态设置和获取正常")
        
        # 测试统计信息
        sm.update_statistics(messages_received=10, messages_sent=5)
        stats = sm.get_statistics()
        print(f"统计信息: {stats}")
        assert stats['messages_received'] >= 10, f"期望 messages_received >= 10, 实际: {stats['messages_received']}"
        assert stats['messages_sent'] >= 5, f"期望 messages_sent >= 5, 实际: {stats['messages_sent']}"
        print("✓ 统计信息更新正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 状态管理器测试失败: {str(e)}")
        return False

def test_thread_communication():
    """测试线程通信"""
    try:
        print("\n测试线程通信...")
        
        from gui.thread_communication import ThreadSafeQueue, Message, MessageType
        
        # 创建队列
        queue = ThreadSafeQueue()
        print("✓ 线程安全队列创建成功")
        
        # 测试消息发送和接收
        msg = Message(MessageType.SERVICE_START, data="test")
        success = queue.put_nowait(msg)
        assert success
        print("✓ 消息发送成功")
        
        received_msg = queue.get_nowait()
        assert received_msg is not None
        assert received_msg.type == MessageType.SERVICE_START
        assert received_msg.data == "test"
        print("✓ 消息接收成功")
        
        queue.close()
        print("✓ 队列关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 线程通信测试失败: {str(e)}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    try:
        print("\n测试GUI创建...")
        
        # 加载配置
        from config import load_config
        load_config()
        print("✓ 配置加载成功")
        
        # 创建根窗口
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("400x300")
        print("✓ 根窗口创建成功")
        
        # 创建GUI管理器
        from gui.gui_manager import GUIManager
        gui_manager = GUIManager(root)
        print("✓ GUI管理器创建成功")
        
        # 显示窗口一秒钟然后关闭
        def close_window():
            gui_manager.cleanup()
            root.quit()
        
        root.after(1000, close_window)  # 1秒后关闭
        root.mainloop()
        
        print("✓ GUI显示和关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI创建测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== GUI基础架构测试 ===\n")
    
    tests = [
        ("导入测试", test_imports),
        ("状态管理器测试", test_state_manager),
        ("线程通信测试", test_thread_communication),
        ("GUI创建测试", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！基础架构搭建成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
