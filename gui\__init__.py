# encoding:utf-8

"""
Dify-on-WeChat GUI模块

该模块提供图形用户界面来管理和监控dify-on-wechat服务。

主要组件:
- GUIManager: 主界面管理器
- ServiceController: 服务控制器
- LogMonitor: 日志监控器
- MessageRecorder: 消息记录器
- ConfigEditor: 配置编辑器
- StatusIndicator: 状态指示器
- StateManager: 状态管理器
- ThreadCommunication: 线程通信

版本: 1.0.0
作者: AI Assistant
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入主要组件
try:
    from .gui_manager import GUIManager
    from .service_controller import ServiceController
    from .log_monitor import LogMonitor
    from .message_recorder import MessageRecorder
    from .config_editor import ConfigEditor
    from .status_indicator import StatusIndicator
    from .state_manager import StateManager
    from .thread_communication import ThreadSafeQueue, MessageType
    
    __all__ = [
        'GUIManager',
        'ServiceController', 
        'LogMonitor',
        'MessageRecorder',
        'ConfigEditor',
        'StatusIndicator',
        'StateManager',
        'ThreadSafeQueue',
        'MessageType'
    ]
    
except ImportError as e:
    # 在开发阶段，某些模块可能还未创建
    # 这里只记录警告，不中断程序
    import warnings
    warnings.warn(f"GUI模块导入警告: {str(e)}", ImportWarning)
    
    __all__ = []
