# encoding:utf-8

"""
服务控制器模块

负责管理后台服务的生命周期，包括启动、暂停、恢复、停止等操作。
与现有app.py逻辑集成，提供GUI控制接口。
"""

import threading
import time
import signal
import sys
import os
from typing import Optional, Callable
from datetime import datetime

from common.log import logger
from config import conf, load_config
from .state_manager import StateManager, ServiceStatus
from .thread_communication import ThreadSafeQueue, Message, MessageType


class ServiceController:
    """
    服务控制器
    
    负责管理后台服务的生命周期，提供启动、暂停、停止等控制功能。
    """
    
    def __init__(self, message_queue: ThreadSafeQueue):
        """
        初始化服务控制器
        
        Args:
            message_queue: 消息队列，用于与GUI通信
        """
        self.message_queue = message_queue
        self.state_manager = StateManager()
        
        # 服务线程和控制
        self.service_thread: Optional[threading.Thread] = None
        self.pause_event = threading.Event()
        self.stop_event = threading.Event()

        # 现有系统组件
        self.channel = None
        self.original_sigint_handler = None
        self.original_sigterm_handler = None

        # 回调函数
        self.status_callback: Optional[Callable] = None
        
        logger.info("[ServiceController] 服务控制器初始化完成")
    
    def start_service(self) -> bool:
        """
        启动服务
        
        Returns:
            bool: 是否成功启动
        """
        try:
            current_status = self.state_manager.get_service_status()
            if current_status in [ServiceStatus.RUNNING, ServiceStatus.STARTING]:
                logger.warning("[ServiceController] 服务已在运行中")
                return False
            
            logger.info("[ServiceController] 开始启动服务...")
            self.state_manager.set_service_status(ServiceStatus.STARTING)
            
            # 重置控制事件
            self.pause_event.clear()
            self.stop_event.clear()
            
            # 创建并启动服务线程
            self.service_thread = threading.Thread(
                target=self._service_worker,
                name="ServiceWorker",
                daemon=True
            )
            self.service_thread.start()
            
            # 等待服务启动
            time.sleep(0.5)  # 给服务一点启动时间
            
            if self.service_thread.is_alive():
                self.state_manager.set_service_status(ServiceStatus.RUNNING)
                logger.info("[ServiceController] 服务启动成功")
                return True
            else:
                self.state_manager.set_service_status(ServiceStatus.ERROR, "服务启动失败")
                logger.error("[ServiceController] 服务启动失败")
                return False
                
        except Exception as e:
            error_msg = f"启动服务时发生错误: {str(e)}"
            logger.error(f"[ServiceController] {error_msg}")
            self.state_manager.set_service_status(ServiceStatus.ERROR, error_msg)
            return False
    
    def pause_service(self) -> bool:
        """
        暂停服务
        
        Returns:
            bool: 是否成功暂停
        """
        try:
            current_status = self.state_manager.get_service_status()
            if current_status != ServiceStatus.RUNNING:
                logger.warning("[ServiceController] 服务未在运行中，无法暂停")
                return False
            
            logger.info("[ServiceController] 暂停服务...")
            self.state_manager.set_service_status(ServiceStatus.PAUSING)
            
            # 设置暂停标志
            self.pause_event.set()
            
            # 等待暂停生效
            time.sleep(0.2)
            
            self.state_manager.set_service_status(ServiceStatus.PAUSED)
            logger.info("[ServiceController] 服务已暂停")
            return True
            
        except Exception as e:
            error_msg = f"暂停服务时发生错误: {str(e)}"
            logger.error(f"[ServiceController] {error_msg}")
            self.state_manager.set_service_status(ServiceStatus.ERROR, error_msg)
            return False
    
    def resume_service(self) -> bool:
        """
        恢复服务
        
        Returns:
            bool: 是否成功恢复
        """
        try:
            current_status = self.state_manager.get_service_status()
            if current_status != ServiceStatus.PAUSED:
                logger.warning("[ServiceController] 服务未暂停，无法恢复")
                return False
            
            logger.info("[ServiceController] 恢复服务...")
            
            # 清除暂停标志
            self.pause_event.clear()
            
            self.state_manager.set_service_status(ServiceStatus.RUNNING)
            logger.info("[ServiceController] 服务已恢复")
            return True
            
        except Exception as e:
            error_msg = f"恢复服务时发生错误: {str(e)}"
            logger.error(f"[ServiceController] {error_msg}")
            self.state_manager.set_service_status(ServiceStatus.ERROR, error_msg)
            return False
    
    def stop_service(self) -> bool:
        """
        停止服务
        
        Returns:
            bool: 是否成功停止
        """
        try:
            current_status = self.state_manager.get_service_status()
            if current_status == ServiceStatus.STOPPED:
                logger.warning("[ServiceController] 服务已停止")
                return True
            
            logger.info("[ServiceController] 停止服务...")
            self.state_manager.set_service_status(ServiceStatus.STOPPING)
            
            # 设置停止标志
            self.stop_event.set()
            self.pause_event.clear()  # 清除暂停标志
            
            # 等待服务线程结束
            if self.service_thread and self.service_thread.is_alive():
                self.service_thread.join(timeout=5.0)
                
                if self.service_thread.is_alive():
                    logger.warning("[ServiceController] 服务线程未能正常结束")
                else:
                    logger.info("[ServiceController] 服务线程已结束")
            
            self.service_thread = None
            self.state_manager.set_service_status(ServiceStatus.STOPPED)
            logger.info("[ServiceController] 服务已停止")
            return True
            
        except Exception as e:
            error_msg = f"停止服务时发生错误: {str(e)}"
            logger.error(f"[ServiceController] {error_msg}")
            self.state_manager.set_service_status(ServiceStatus.ERROR, error_msg)
            return False
    
    def get_service_status(self) -> ServiceStatus:
        """获取服务状态"""
        return self.state_manager.get_service_status()
    
    def is_running(self) -> bool:
        """检查服务是否正在运行"""
        return self.get_service_status() == ServiceStatus.RUNNING
    
    def is_paused(self) -> bool:
        """检查服务是否已暂停"""
        return self.get_service_status() == ServiceStatus.PAUSED
    
    def _service_worker(self):
        """
        服务工作线程

        集成现有的app.py逻辑，真正启动dify-on-wechat服务。
        """
        try:
            logger.info("[ServiceController] 服务工作线程启动")

            # 设置信号处理器
            self._setup_signal_handlers()

            # 加载配置
            load_config()

            # 创建通道
            channel_name = conf().get("channel_type", "wx")
            logger.info(f"[ServiceController] 创建通道: {channel_name}")

            # 导入并创建通道
            from channel import channel_factory
            self.channel = channel_factory.create_channel(channel_name)

            # 加载插件
            if channel_name in ["wx", "wxy", "terminal", "wechatmp", "wechatmp_service",
                               "wechatcom_app", "wework", "wechatcom_service", "gewechat", "web"]:
                try:
                    from plugins import PluginManager
                    PluginManager().load_plugins()
                    logger.info("[ServiceController] 插件加载完成")
                except Exception as e:
                    logger.warning(f"[ServiceController] 插件加载失败: {str(e)}")

            # 启动linkai（如果配置了）
            if conf().get("use_linkai"):
                try:
                    from common import linkai_client
                    threading.Thread(target=linkai_client.start, args=(self.channel,)).start()
                    logger.info("[ServiceController] Linkai客户端启动")
                except Exception as e:
                    logger.warning(f"[ServiceController] Linkai客户端启动失败: {str(e)}")

            # 启动通道
            logger.info("[ServiceController] 启动通道...")
            self.channel.startup()

            # 主服务循环
            logger.info("[ServiceController] 进入主服务循环")
            message_count = 0
            while not self.stop_event.is_set():
                # 检查暂停状态
                if self.pause_event.is_set():
                    time.sleep(0.1)
                    continue

                # 更新统计信息（模拟）
                message_count += 1
                if message_count % 100 == 0:  # 每100次循环更新一次统计
                    self.state_manager.update_statistics(
                        messages_received=1,
                        messages_sent=1
                    )

                    # 发送状态更新消息
                    status_msg = Message(
                        MessageType.STATUS_UPDATE,
                        data={
                            'timestamp': datetime.now().isoformat(),
                            'message_count': message_count
                        }
                    )
                    self.message_queue.put_nowait(status_msg)

                # 短暂休眠，避免CPU占用过高
                time.sleep(1)

            logger.info("[ServiceController] 服务工作线程正常结束")

        except Exception as e:
            error_msg = f"服务工作线程异常: {str(e)}"
            logger.error(f"[ServiceController] {error_msg}")
            logger.exception(e)
            self.state_manager.set_service_status(ServiceStatus.ERROR, error_msg)

            # 发送错误消息
            error_msg_obj = Message(
                MessageType.ERROR_MESSAGE,
                data={'error': error_msg, 'timestamp': datetime.now().isoformat()}
            )
            self.message_queue.put_nowait(error_msg_obj)
        finally:
            # 恢复信号处理器
            self._restore_signal_handlers()

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            # 保存原始信号处理器
            self.original_sigint_handler = signal.signal(signal.SIGINT, self._signal_handler)
            self.original_sigterm_handler = signal.signal(signal.SIGTERM, self._signal_handler)
            logger.debug("[ServiceController] 信号处理器设置完成")
        except Exception as e:
            logger.warning(f"[ServiceController] 设置信号处理器失败: {str(e)}")

    def _restore_signal_handlers(self):
        """恢复原始信号处理器"""
        try:
            if self.original_sigint_handler:
                signal.signal(signal.SIGINT, self.original_sigint_handler)
            if self.original_sigterm_handler:
                signal.signal(signal.SIGTERM, self.original_sigterm_handler)
            logger.debug("[ServiceController] 信号处理器恢复完成")
        except Exception as e:
            logger.warning(f"[ServiceController] 恢复信号处理器失败: {str(e)}")

    def _signal_handler(self, signo, frame):
        """信号处理函数"""
        logger.info(f"[ServiceController] 接收到信号 {signo}，开始停止服务...")
        self.stop_service()
    
    def set_status_callback(self, callback: Callable):
        """设置状态变更回调函数"""
        self.status_callback = callback
    
    def cleanup(self):
        """清理资源"""
        try:
            logger.info("[ServiceController] 开始清理服务控制器...")
            
            # 停止服务
            if self.get_service_status() != ServiceStatus.STOPPED:
                self.stop_service()
            
            logger.info("[ServiceController] 服务控制器清理完成")
            
        except Exception as e:
            logger.error(f"[ServiceController] 清理时发生错误: {str(e)}")
