# encoding:utf-8

"""
消息记录器模块（占位符实现）

负责记录和显示接收/发送的消息，提供搜索和过滤功能。
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import queue
import threading

from common.log import logger


class MessageInterceptor:
    """
    消息拦截器

    拦截系统中的消息并发送到GUI显示队列。
    """

    def __init__(self, message_queue: queue.Queue):
        """
        初始化消息拦截器

        Args:
            message_queue: 消息队列
        """
        self.message_queue = message_queue
        self.enabled = True

    def intercept_received_message(self, context, cmsg):
        """
        拦截接收到的消息

        Args:
            context: 消息上下文
            cmsg: 聊天消息对象
        """
        if not self.enabled:
            return

        try:
            message_data = {
                "type": "接收",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "user": getattr(cmsg, 'from_user_nickname', '') or getattr(cmsg, 'from_user_id', '未知用户'),
                "content": str(cmsg.content) if cmsg.content else "",
                "raw_data": {
                    "msg_id": getattr(cmsg, 'msg_id', ''),
                    "ctype": str(getattr(cmsg, 'ctype', '')),
                    "is_group": getattr(cmsg, 'is_group', False),
                    "group_name": getattr(cmsg, 'other_user_nickname', '') if getattr(cmsg, 'is_group', False) else '',
                    "from_user_id": getattr(cmsg, 'from_user_id', ''),
                    "to_user_id": getattr(cmsg, 'to_user_id', '')
                }
            }

            self.message_queue.put_nowait(message_data)

        except queue.Full:
            # 队列满时丢弃消息
            pass
        except Exception as e:
            logger.error(f"[MessageInterceptor] 拦截接收消息失败: {str(e)}")

    def intercept_sent_message(self, context, reply):
        """
        拦截发送的消息

        Args:
            context: 消息上下文
            reply: 回复对象
        """
        if not self.enabled:
            return

        try:
            # 获取接收者信息
            receiver = context.get('receiver', '未知接收者')
            receiver_nickname = context.get('receiver_nickname', receiver)

            message_data = {
                "type": "发送",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "user": receiver_nickname,
                "content": str(reply.content) if reply.content else "",
                "raw_data": {
                    "reply_type": str(reply.type),
                    "receiver": receiver,
                    "session_id": context.get('session_id', ''),
                    "is_group": context.get('is_group', False)
                }
            }

            self.message_queue.put_nowait(message_data)

        except queue.Full:
            # 队列满时丢弃消息
            pass
        except Exception as e:
            logger.error(f"[MessageInterceptor] 拦截发送消息失败: {str(e)}")

    def enable(self):
        """启用消息拦截"""
        self.enabled = True

    def disable(self):
        """禁用消息拦截"""
        self.enabled = False


class MessageRecorder:
    """
    消息记录器

    负责记录和显示聊天消息，提供搜索和导出功能。
    """

    def __init__(self, parent_frame: ttk.Frame):
        """
        初始化消息记录器

        Args:
            parent_frame: 父框架
        """
        self.parent_frame = parent_frame
        self.message_tree = None
        self.search_var = None
        self.filter_var = None

        # 消息缓存
        self.messages: List[Dict[str, Any]] = []
        self.max_messages = 1000

        # 消息队列和拦截器
        self.message_queue = queue.Queue(maxsize=2000)
        self.message_interceptor = None
        self.monitoring = False
        self.monitor_thread = None

        self._create_interface()
        self._add_sample_messages()
        self._start_monitoring()

        logger.info("[MessageRecorder] 消息记录器初始化完成")
    
    def _create_interface(self):
        """创建消息记录界面"""
        # 控制栏
        control_frame = ttk.Frame(self.parent_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 搜索框
        ttk.Label(control_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(
            control_frame,
            textvariable=self.search_var,
            width=20
        )
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind("<KeyRelease>", self._on_search_changed)
        
        # 消息类型过滤
        ttk.Label(control_frame, text="类型:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.filter_var = tk.StringVar(value="ALL")
        filter_combo = ttk.Combobox(
            control_frame,
            textvariable=self.filter_var,
            values=["ALL", "接收", "发送"],
            state="readonly",
            width=8
        )
        filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        filter_combo.bind("<<ComboboxSelected>>", self._on_filter_changed)
        
        # 清空按钮
        clear_button = ttk.Button(
            control_frame,
            text="清空",
            command=self._clear_messages,
            width=8
        )
        clear_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出按钮
        export_button = ttk.Button(
            control_frame,
            text="导出",
            command=self._export_messages,
            width=8
        )
        export_button.pack(side=tk.LEFT)
        
        # 消息列表
        list_frame = ttk.Frame(self.parent_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 创建Treeview
        columns = ("时间", "类型", "用户", "内容")
        self.message_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # 设置列标题和宽度
        self.message_tree.heading("时间", text="时间")
        self.message_tree.heading("类型", text="类型")
        self.message_tree.heading("用户", text="用户")
        self.message_tree.heading("内容", text="内容")
        
        self.message_tree.column("时间", width=150, minwidth=120)
        self.message_tree.column("类型", width=60, minwidth=50)
        self.message_tree.column("用户", width=100, minwidth=80)
        self.message_tree.column("内容", width=300, minwidth=200)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.message_tree.yview)
        self.message_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.message_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.message_tree.bind("<Double-1>", self._on_message_double_click)

    def _start_monitoring(self):
        """开始监控消息"""
        try:
            # 创建消息拦截器
            self.message_interceptor = MessageInterceptor(self.message_queue)

            # 启动监控线程
            self.monitoring = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()

            logger.info("[MessageRecorder] 消息监控已启动")

        except Exception as e:
            logger.error(f"[MessageRecorder] 启动消息监控失败: {str(e)}")

    def _stop_monitoring(self):
        """停止监控消息"""
        try:
            self.monitoring = False

            # 禁用消息拦截器
            if self.message_interceptor:
                self.message_interceptor.disable()
                self.message_interceptor = None

            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=1.0)

            logger.info("[MessageRecorder] 消息监控已停止")

        except Exception as e:
            logger.error(f"[MessageRecorder] 停止消息监控失败: {str(e)}")

    def _monitor_worker(self):
        """监控工作线程"""
        while self.monitoring:
            try:
                # 从队列获取消息
                message_data = self.message_queue.get(timeout=0.1)

                # 在主线程中更新GUI
                if hasattr(self.parent_frame, 'after'):
                    self.parent_frame.after(0, self._add_intercepted_message, message_data)

            except queue.Empty:
                continue
            except Exception as e:
                # 记录监控线程错误，但不中断监控
                print(f"[MessageRecorder] 监控线程错误: {str(e)}")
                continue

    def _add_intercepted_message(self, message_data: Dict[str, Any]):
        """添加拦截到的消息"""
        try:
            # 添加到消息列表
            self.messages.append(message_data)

            # 限制消息数量
            if len(self.messages) > self.max_messages:
                self.messages.pop(0)
                # 同时清理GUI中的第一行
                items = self.message_tree.get_children()
                if items:
                    self.message_tree.delete(items[0])

            # 添加到GUI显示
            self._add_message_to_list(message_data)

        except Exception as e:
            logger.error(f"[MessageRecorder] 添加拦截消息失败: {str(e)}")
    
    def _add_sample_messages(self):
        """添加示例消息"""
        sample_messages = [
            {
                "timestamp": "2025-08-19 05:00:00",
                "type": "接收",
                "user": "用户001",
                "content": "你好，请问今天天气怎么样？",
                "raw_data": {"from": "user001", "to": "bot"}
            },
            {
                "timestamp": "2025-08-19 05:00:01",
                "type": "发送",
                "user": "机器人",
                "content": "今天天气晴朗，温度适宜，是个不错的天气。",
                "raw_data": {"from": "bot", "to": "user001"}
            },
            {
                "timestamp": "2025-08-19 05:00:30",
                "type": "接收",
                "user": "用户002",
                "content": "帮我查询一下明天的会议安排",
                "raw_data": {"from": "user002", "to": "bot"}
            },
            {
                "timestamp": "2025-08-19 05:00:31",
                "type": "发送",
                "user": "机器人",
                "content": "明天您有以下会议安排：\n1. 上午9:00 - 项目讨论会\n2. 下午2:00 - 客户会议",
                "raw_data": {"from": "bot", "to": "user002"}
            },
            {
                "timestamp": "2025-08-19 05:01:00",
                "type": "接收",
                "user": "用户001",
                "content": "谢谢！",
                "raw_data": {"from": "user001", "to": "bot"}
            }
        ]
        
        for msg in sample_messages:
            self._add_message_to_list(msg)
            self.messages.append(msg)
    
    def _add_message_to_list(self, message: Dict[str, Any]):
        """添加消息到列表"""
        # 检查过滤器
        current_filter = self.filter_var.get()
        if current_filter != "ALL" and current_filter != message["type"]:
            return
        
        # 检查搜索
        search_text = self.search_var.get().lower()
        if search_text and search_text not in message["content"].lower():
            return
        
        # 添加到树形控件
        self.message_tree.insert(
            "",
            tk.END,
            values=(
                message["timestamp"],
                message["type"],
                message["user"],
                message["content"][:50] + "..." if len(message["content"]) > 50 else message["content"]
            )
        )
        
        # 自动滚动到底部
        items = self.message_tree.get_children()
        if items:
            self.message_tree.see(items[-1])
    
    def _refresh_display(self):
        """刷新显示"""
        # 清空当前显示
        for item in self.message_tree.get_children():
            self.message_tree.delete(item)
        
        # 重新添加符合条件的消息
        for message in self.messages:
            self._add_message_to_list(message)
    
    def _on_search_changed(self, event=None):
        """处理搜索变更"""
        self._refresh_display()
    
    def _on_filter_changed(self, event=None):
        """处理过滤器变更"""
        self._refresh_display()
    
    def _on_message_double_click(self, event):
        """处理消息双击事件"""
        selection = self.message_tree.selection()
        if not selection:
            return
        
        item = self.message_tree.item(selection[0])
        values = item["values"]
        
        if len(values) >= 4:
            # 显示完整消息内容
            self._show_message_detail(values[0], values[1], values[2], values[3])
    
    def _show_message_detail(self, timestamp: str, msg_type: str, user: str, content: str):
        """显示消息详情"""
        # 查找完整消息
        full_content = content
        for msg in self.messages:
            if (msg["timestamp"] == timestamp and 
                msg["type"] == msg_type and 
                msg["user"] == user):
                full_content = msg["content"]
                break
        
        # 创建详情窗口
        detail_window = tk.Toplevel(self.parent_frame)
        detail_window.title("消息详情")
        detail_window.geometry("500x400")
        detail_window.transient(self.parent_frame.winfo_toplevel())
        detail_window.grab_set()
        
        # 消息信息
        info_frame = ttk.Frame(detail_window)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(info_frame, text=f"时间: {timestamp}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"类型: {msg_type}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"用户: {user}").pack(anchor=tk.W)
        
        # 消息内容
        content_frame = ttk.LabelFrame(detail_window, text="消息内容", padding=10)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        content_text = tk.Text(
            content_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10),
            state=tk.DISABLED
        )
        content_text.pack(fill=tk.BOTH, expand=True)
        
        content_text.config(state=tk.NORMAL)
        content_text.insert(tk.END, full_content)
        content_text.config(state=tk.DISABLED)
        
        # 关闭按钮
        button_frame = ttk.Frame(detail_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(
            button_frame,
            text="关闭",
            command=detail_window.destroy
        ).pack(side=tk.RIGHT)
    
    def _clear_messages(self):
        """清空消息"""
        if messagebox.askyesno("确认", "确定要清空所有消息记录吗？"):
            self.messages.clear()
            for item in self.message_tree.get_children():
                self.message_tree.delete(item)
            logger.info("[MessageRecorder] 消息记录已清空")
    
    def _export_messages(self):
        """导出消息"""
        try:
            from tkinter import filedialog
            
            filename = filedialog.asksaveasfilename(
                title="导出消息记录",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if filename:
                if filename.endswith('.json'):
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(self.messages, f, ensure_ascii=False, indent=2)
                else:
                    with open(filename, 'w', encoding='utf-8') as f:
                        for msg in self.messages:
                            f.write(f"[{msg['timestamp']}] {msg['type']} - {msg['user']}: {msg['content']}\n")
                
                messagebox.showinfo("成功", f"消息记录已导出到: {filename}")
                logger.info(f"[MessageRecorder] 消息记录已导出到: {filename}")
                
        except Exception as e:
            error_msg = f"导出消息记录失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            logger.error(f"[MessageRecorder] {error_msg}")
    
    def add_message(self, msg_type: str, user: str, content: str, raw_data: Dict = None):
        """
        添加新消息
        
        Args:
            msg_type: 消息类型（接收/发送）
            user: 用户名
            content: 消息内容
            raw_data: 原始数据
        """
        message = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "type": msg_type,
            "user": user,
            "content": content,
            "raw_data": raw_data or {}
        }
        
        self.messages.append(message)
        
        # 限制消息数量
        if len(self.messages) > self.max_messages:
            self.messages.pop(0)
        
        self._add_message_to_list(message)
    
    def get_message_interceptor(self) -> Optional[MessageInterceptor]:
        """获取消息拦截器"""
        return self.message_interceptor

    def cleanup(self):
        """清理资源"""
        try:
            # 停止监控
            self._stop_monitoring()

            logger.info("[MessageRecorder] 消息记录器清理完成")

        except Exception as e:
            logger.error(f"[MessageRecorder] 清理时发生错误: {str(e)}")
