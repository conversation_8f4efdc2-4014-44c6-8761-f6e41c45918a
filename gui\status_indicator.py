# encoding:utf-8

"""
状态指示器模块

提供实时状态显示，包括颜色指示器和状态动画效果。
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any
import threading
import time

from common.log import logger
from .state_manager import StateManager, ServiceStatus, GUIState


class StatusIndicator:
    """
    状态指示器
    
    提供实时状态显示，包括颜色指示和动画效果。
    """
    
    def __init__(self, parent_widget: tk.Widget):
        """
        初始化状态指示器
        
        Args:
            parent_widget: 父控件
        """
        self.parent_widget = parent_widget
        self.state_manager = StateManager()
        
        # 状态颜色映射
        self.status_colors = {
            ServiceStatus.STOPPED: "#FF6B6B",      # 红色
            ServiceStatus.STARTING: "#FFE66D",     # 黄色
            ServiceStatus.RUNNING: "#4ECDC4",      # 绿色
            ServiceStatus.PAUSING: "#FFE66D",      # 黄色
            ServiceStatus.PAUSED: "#95E1D3",       # 浅绿色
            ServiceStatus.STOPPING: "#FFE66D",     # 黄色
            ServiceStatus.ERROR: "#FF3838"         # 深红色
        }
        
        # 动画控制
        self.animation_running = False
        self.animation_thread = None
        
        # 监听状态变更
        self.state_manager.add_listener('service_status', self._on_status_changed)
        
        logger.info("[StatusIndicator] 状态指示器初始化完成")
    
    def create_status_canvas(self, parent: tk.Widget, size: int = 20) -> tk.Canvas:
        """
        创建状态指示画布
        
        Args:
            parent: 父控件
            size: 指示器大小
            
        Returns:
            tk.Canvas: 状态画布
        """
        canvas = tk.Canvas(
            parent,
            width=size,
            height=size,
            highlightthickness=0,
            relief=tk.FLAT
        )
        
        # 绘制初始状态
        self._draw_status_indicator(canvas, ServiceStatus.STOPPED, size)
        
        return canvas
    
    def create_status_label(self, parent: tk.Widget, **kwargs) -> ttk.Label:
        """
        创建状态文本标签
        
        Args:
            parent: 父控件
            **kwargs: 标签参数
            
        Returns:
            ttk.Label: 状态标签
        """
        label = ttk.Label(parent, **kwargs)
        
        # 设置初始状态
        self._update_status_label(label, ServiceStatus.STOPPED)
        
        return label
    
    def create_status_frame(self, parent: tk.Widget) -> ttk.Frame:
        """
        创建完整的状态显示框架
        
        Args:
            parent: 父控件
            
        Returns:
            ttk.Frame: 状态框架
        """
        frame = ttk.Frame(parent)
        
        # 状态指示器
        self.status_canvas = self.create_status_canvas(frame, 16)
        self.status_canvas.pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态文本
        self.status_label = self.create_status_label(frame)
        self.status_label.pack(side=tk.LEFT)
        
        return frame
    
    def _draw_status_indicator(self, canvas: tk.Canvas, status: ServiceStatus, size: int):
        """
        绘制状态指示器
        
        Args:
            canvas: 画布
            status: 服务状态
            size: 指示器大小
        """
        canvas.delete("all")
        
        color = self.status_colors.get(status, "#CCCCCC")
        
        # 绘制圆形指示器
        margin = 2
        canvas.create_oval(
            margin, margin,
            size - margin, size - margin,
            fill=color,
            outline=color,
            width=1
        )
        
        # 对于过渡状态，添加内圈动画效果
        if status in [ServiceStatus.STARTING, ServiceStatus.PAUSING, ServiceStatus.STOPPING]:
            inner_margin = size // 4
            canvas.create_oval(
                inner_margin, inner_margin,
                size - inner_margin, size - inner_margin,
                fill="white",
                outline="white",
                width=1,
                tags="inner"
            )
    
    def _update_status_label(self, label: ttk.Label, status: ServiceStatus):
        """
        更新状态标签
        
        Args:
            label: 标签控件
            status: 服务状态
        """
        status_text = {
            ServiceStatus.STOPPED: "已停止",
            ServiceStatus.STARTING: "启动中...",
            ServiceStatus.RUNNING: "运行中",
            ServiceStatus.PAUSING: "暂停中...",
            ServiceStatus.PAUSED: "已暂停",
            ServiceStatus.STOPPING: "停止中...",
            ServiceStatus.ERROR: "错误"
        }
        
        text = status_text.get(status, "未知状态")
        label.config(text=text)
    
    def _on_status_changed(self, event_type: str, old_status: ServiceStatus, new_status: ServiceStatus):
        """
        处理状态变更
        
        Args:
            event_type: 事件类型
            old_status: 旧状态
            new_status: 新状态
        """
        # 更新所有状态指示器
        if hasattr(self, 'status_canvas'):
            self._draw_status_indicator(self.status_canvas, new_status, 16)
        
        if hasattr(self, 'status_label'):
            self._update_status_label(self.status_label, new_status)
        
        # 启动或停止动画
        if new_status in [ServiceStatus.STARTING, ServiceStatus.PAUSING, ServiceStatus.STOPPING]:
            self._start_animation()
        else:
            self._stop_animation()
    
    def _start_animation(self):
        """启动状态动画"""
        if not self.animation_running:
            self.animation_running = True
            self.animation_thread = threading.Thread(
                target=self._animation_worker,
                daemon=True
            )
            self.animation_thread.start()
    
    def _stop_animation(self):
        """停止状态动画"""
        self.animation_running = False
        if self.animation_thread:
            self.animation_thread.join(timeout=1.0)
            self.animation_thread = None
    
    def _animation_worker(self):
        """动画工作线程"""
        try:
            while self.animation_running:
                if hasattr(self, 'status_canvas'):
                    # 在主线程中更新动画
                    self.parent_widget.after(0, self._animate_indicator)
                
                time.sleep(0.5)  # 动画间隔
                
        except Exception as e:
            logger.error(f"[StatusIndicator] 动画线程错误: {str(e)}")
    
    def _animate_indicator(self):
        """动画指示器"""
        if not hasattr(self, 'status_canvas'):
            return
        
        try:
            # 获取当前状态
            current_status = self.state_manager.get_service_status()
            
            # 只对过渡状态进行动画
            if current_status not in [ServiceStatus.STARTING, ServiceStatus.PAUSING, ServiceStatus.STOPPING]:
                return
            
            # 切换内圈显示状态
            inner_items = self.status_canvas.find_withtag("inner")
            if inner_items:
                # 隐藏内圈
                self.status_canvas.delete("inner")
            else:
                # 显示内圈
                size = 16
                inner_margin = size // 4
                self.status_canvas.create_oval(
                    inner_margin, inner_margin,
                    size - inner_margin, size - inner_margin,
                    fill="white",
                    outline="white",
                    width=1,
                    tags="inner"
                )
                
        except Exception as e:
            logger.error(f"[StatusIndicator] 动画更新错误: {str(e)}")
    
    def get_status_color(self, status: ServiceStatus) -> str:
        """
        获取状态对应的颜色
        
        Args:
            status: 服务状态
            
        Returns:
            str: 颜色代码
        """
        return self.status_colors.get(status, "#CCCCCC")
    
    def get_status_text(self, status: ServiceStatus) -> str:
        """
        获取状态对应的文本
        
        Args:
            status: 服务状态
            
        Returns:
            str: 状态文本
        """
        status_text = {
            ServiceStatus.STOPPED: "已停止",
            ServiceStatus.STARTING: "启动中",
            ServiceStatus.RUNNING: "运行中",
            ServiceStatus.PAUSING: "暂停中",
            ServiceStatus.PAUSED: "已暂停",
            ServiceStatus.STOPPING: "停止中",
            ServiceStatus.ERROR: "错误"
        }
        
        return status_text.get(status, "未知状态")
    
    def cleanup(self):
        """清理资源"""
        try:
            logger.info("[StatusIndicator] 开始清理状态指示器...")
            
            # 停止动画
            self._stop_animation()
            
            # 移除状态监听器
            self.state_manager.remove_listener('service_status', self._on_status_changed)
            
            logger.info("[StatusIndicator] 状态指示器清理完成")
            
        except Exception as e:
            logger.error(f"[StatusIndicator] 清理时发生错误: {str(e)}")
