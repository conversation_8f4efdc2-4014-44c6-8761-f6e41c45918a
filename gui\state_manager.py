# encoding:utf-8

"""
状态管理器模块

负责管理GUI应用程序的全局状态，包括服务状态、配置状态等。
提供状态持久化、状态变更通知等功能。
"""

import os
import json
import threading
from enum import Enum
from typing import Dict, Any, Callable, Optional
from datetime import datetime

from common.log import logger
from config import get_appdata_dir


class ServiceStatus(Enum):
    """服务状态枚举"""
    STOPPED = "stopped"      # 已停止
    STARTING = "starting"    # 启动中
    RUNNING = "running"      # 运行中
    PAUSING = "pausing"      # 暂停中
    PAUSED = "paused"        # 已暂停
    STOPPING = "stopping"   # 停止中
    ERROR = "error"          # 错误状态


class GUIState(Enum):
    """GUI状态枚举"""
    INITIALIZING = "initializing"  # 初始化中
    READY = "ready"               # 就绪
    BUSY = "busy"                 # 忙碌中
    ERROR = "error"               # 错误状态


class StateManager:
    """
    状态管理器
    
    负责管理应用程序的全局状态，提供状态持久化和变更通知功能。
    使用单例模式确保全局唯一性。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(StateManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化状态管理器"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self._lock = threading.RLock()
        self._state_file = os.path.join(get_appdata_dir(), "gui_state.json")
        
        # 状态数据
        self._states = {
            'service_status': ServiceStatus.STOPPED,
            'gui_state': GUIState.INITIALIZING,
            'last_update': datetime.now().isoformat(),
            'service_start_time': None,
            'error_message': None,
            'statistics': {
                'messages_received': 0,
                'messages_sent': 0,
                'errors_count': 0,
                'uptime_seconds': 0
            }
        }
        
        # 状态变更监听器
        self._listeners = {}
        
        # 加载持久化状态
        self._load_state()
        
        logger.info("[StateManager] 状态管理器初始化完成")
    
    def get_service_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self._states['service_status']
    
    def set_service_status(self, status: ServiceStatus, error_message: Optional[str] = None):
        """设置服务状态"""
        with self._lock:
            old_status = self._states['service_status']
            self._states['service_status'] = status
            self._states['last_update'] = datetime.now().isoformat()
            
            if error_message:
                self._states['error_message'] = error_message
            elif status != ServiceStatus.ERROR:
                self._states['error_message'] = None
            
            # 记录服务启动时间
            if status == ServiceStatus.RUNNING and old_status != ServiceStatus.RUNNING:
                self._states['service_start_time'] = datetime.now().isoformat()
            elif status == ServiceStatus.STOPPED:
                self._states['service_start_time'] = None
            
            logger.info(f"[StateManager] 服务状态变更: {old_status.value} -> {status.value}")
            
            # 通知监听器
            self._notify_listeners('service_status', old_status, status)
            
            # 保存状态
            self._save_state()
    
    def get_gui_state(self) -> GUIState:
        """获取GUI状态"""
        with self._lock:
            return self._states['gui_state']
    
    def set_gui_state(self, state: GUIState):
        """设置GUI状态"""
        with self._lock:
            old_state = self._states['gui_state']
            self._states['gui_state'] = state
            self._states['last_update'] = datetime.now().isoformat()
            
            logger.debug(f"[StateManager] GUI状态变更: {old_state.value} -> {state.value}")
            
            # 通知监听器
            self._notify_listeners('gui_state', old_state, state)
    
    def get_error_message(self) -> Optional[str]:
        """获取错误消息"""
        with self._lock:
            return self._states.get('error_message')
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self._states['statistics'].copy()
            
            # 计算运行时间
            if self._states['service_start_time']:
                start_time = datetime.fromisoformat(self._states['service_start_time'])
                uptime = (datetime.now() - start_time).total_seconds()
                stats['uptime_seconds'] = int(uptime)
            
            return stats
    
    def update_statistics(self, **kwargs):
        """更新统计信息"""
        with self._lock:
            for key, value in kwargs.items():
                if key in self._states['statistics']:
                    if isinstance(value, int) and value > 0:
                        self._states['statistics'][key] += value
                    else:
                        self._states['statistics'][key] = value
            
            self._states['last_update'] = datetime.now().isoformat()
            
            # 通知监听器
            self._notify_listeners('statistics', None, self._states['statistics'])
    
    def add_listener(self, event_type: str, callback: Callable):
        """添加状态变更监听器"""
        with self._lock:
            if event_type not in self._listeners:
                self._listeners[event_type] = []
            self._listeners[event_type].append(callback)
            
            logger.debug(f"[StateManager] 添加监听器: {event_type}")
    
    def remove_listener(self, event_type: str, callback: Callable):
        """移除状态变更监听器"""
        with self._lock:
            if event_type in self._listeners:
                try:
                    self._listeners[event_type].remove(callback)
                    logger.debug(f"[StateManager] 移除监听器: {event_type}")
                except ValueError:
                    pass
    
    def _notify_listeners(self, event_type: str, old_value: Any, new_value: Any):
        """通知状态变更监听器"""
        if event_type in self._listeners:
            for callback in self._listeners[event_type][:]:  # 复制列表避免并发修改
                try:
                    callback(event_type, old_value, new_value)
                except Exception as e:
                    logger.error(f"[StateManager] 监听器回调错误: {str(e)}")
    
    def _load_state(self):
        """加载持久化状态"""
        try:
            if os.path.exists(self._state_file):
                with open(self._state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复状态（除了运行状态）
                if 'statistics' in data:
                    self._states['statistics'].update(data['statistics'])
                
                # 服务状态重置为停止状态（重启后不应该是运行状态）
                self._states['service_status'] = ServiceStatus.STOPPED
                
                logger.info("[StateManager] 状态数据加载完成")
        except Exception as e:
            logger.warning(f"[StateManager] 加载状态文件失败: {str(e)}")
    
    def _save_state(self):
        """保存状态到文件"""
        try:
            # 准备序列化数据
            data = {
                'service_status': self._states['service_status'].value,
                'gui_state': self._states['gui_state'].value,
                'last_update': self._states['last_update'],
                'service_start_time': self._states['service_start_time'],
                'error_message': self._states['error_message'],
                'statistics': self._states['statistics']
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self._state_file), exist_ok=True)
            
            # 写入文件
            with open(self._state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"[StateManager] 保存状态文件失败: {str(e)}")
    
    def reset_statistics(self):
        """重置统计信息"""
        with self._lock:
            self._states['statistics'] = {
                'messages_received': 0,
                'messages_sent': 0,
                'errors_count': 0,
                'uptime_seconds': 0
            }
            self._save_state()
            logger.info("[StateManager] 统计信息已重置")
    
    def get_all_states(self) -> Dict[str, Any]:
        """获取所有状态信息"""
        with self._lock:
            return {
                'service_status': self._states['service_status'],
                'gui_state': self._states['gui_state'],
                'last_update': self._states['last_update'],
                'service_start_time': self._states['service_start_time'],
                'error_message': self._states['error_message'],
                'statistics': self.get_statistics()
            }
