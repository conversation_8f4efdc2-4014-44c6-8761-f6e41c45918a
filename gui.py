# encoding:utf-8

"""
Dify-on-WeChat GUI应用程序主入口
提供图形用户界面来管理和监控dify-on-wechat服务

作者: AI Assistant
版本: 1.0.0
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import threading
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入现有项目模块
    from common.log import logger
    from config import conf, load_config
    
    # 导入GUI模块
    from gui.gui_manager import GUIManager
    
except ImportError as e:
    # 如果导入失败，显示错误信息
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    messagebox.showerror(
        "导入错误", 
        f"无法导入必要的模块:\n{str(e)}\n\n请确保所有依赖已正确安装。"
    )
    sys.exit(1)


class DifyGUIApplication:
    """
    Dify GUI应用程序主类
    负责应用程序的初始化、启动和生命周期管理
    """
    
    def __init__(self):
        """初始化GUI应用程序"""
        self.root = None
        self.gui_manager = None
        self.is_running = False
        
    def initialize(self):
        """初始化应用程序组件"""
        try:
            # 加载配置
            load_config()
            logger.info("[GUI] 开始初始化GUI应用程序...")
            
            # 创建主窗口
            self.root = tk.Tk()
            self.root.title("Dify-on-WeChat 管理界面")
            self.root.geometry("1200x800")
            self.root.minsize(800, 600)
            
            # 设置窗口图标（如果存在）
            try:
                icon_path = os.path.join(os.path.dirname(__file__), "assets", "icon.ico")
                if os.path.exists(icon_path):
                    self.root.iconbitmap(icon_path)
            except Exception:
                pass  # 忽略图标加载错误
            
            # 创建GUI管理器
            self.gui_manager = GUIManager(self.root)
            
            # 绑定窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 设置异常处理
            self.root.report_callback_exception = self.handle_exception
            
            logger.info("[GUI] GUI应用程序初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"[GUI] 初始化失败: {str(e)}")
            logger.exception(e)
            self.show_error("初始化失败", f"GUI应用程序初始化失败:\n{str(e)}")
            return False
    
    def run(self):
        """运行GUI应用程序"""
        if not self.initialize():
            return
            
        try:
            self.is_running = True
            logger.info("[GUI] 启动GUI应用程序主循环...")
            
            # 启动Tkinter主循环
            self.root.mainloop()
            
        except Exception as e:
            logger.error(f"[GUI] 运行时错误: {str(e)}")
            logger.exception(e)
            self.show_error("运行时错误", f"GUI应用程序运行时发生错误:\n{str(e)}")
        finally:
            self.cleanup()
    
    def on_closing(self):
        """处理窗口关闭事件"""
        try:
            logger.info("[GUI] 用户请求关闭应用程序...")
            
            # 如果GUI管理器存在，先进行清理
            if self.gui_manager:
                if not self.gui_manager.on_closing():
                    # 如果GUI管理器返回False，取消关闭
                    return
            
            # 确认关闭
            if messagebox.askokcancel("退出确认", "确定要退出Dify-on-WeChat管理界面吗？"):
                self.is_running = False
                self.root.quit()
                
        except Exception as e:
            logger.error(f"[GUI] 关闭处理错误: {str(e)}")
            logger.exception(e)
            # 强制退出
            self.root.quit()
    
    def cleanup(self):
        """清理资源"""
        try:
            logger.info("[GUI] 开始清理资源...")
            
            if self.gui_manager:
                self.gui_manager.cleanup()
                self.gui_manager = None
            
            if self.root:
                self.root.destroy()
                self.root = None
                
            logger.info("[GUI] 资源清理完成")
            
        except Exception as e:
            logger.error(f"[GUI] 清理资源时发生错误: {str(e)}")
            logger.exception(e)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 处理Ctrl+C中断
            self.on_closing()
            return
            
        # 记录异常
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"[GUI] 未捕获的异常: {error_msg}")
        
        # 显示错误对话框
        self.show_error(
            "程序错误", 
            f"程序发生未处理的错误:\n\n{exc_type.__name__}: {str(exc_value)}\n\n"
            f"详细信息已记录到日志文件中。"
        )
    
    def show_error(self, title, message):
        """显示错误对话框"""
        try:
            if self.root and self.root.winfo_exists():
                messagebox.showerror(title, message, parent=self.root)
            else:
                # 如果主窗口不存在，创建临时窗口
                temp_root = tk.Tk()
                temp_root.withdraw()
                messagebox.showerror(title, message, parent=temp_root)
                temp_root.destroy()
        except Exception:
            # 如果GUI显示失败，至少打印到控制台
            print(f"ERROR: {title} - {message}")


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)
        
        # 检查是否在正确的目录中运行
        if not os.path.exists("config.py"):
            print("错误: 请在dify-on-wechat项目根目录中运行此程序")
            sys.exit(1)
        
        # 创建并运行GUI应用程序
        app = DifyGUIApplication()
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"启动失败: {str(e)}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
