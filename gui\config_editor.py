# encoding:utf-8

"""
配置编辑器模块（占位符实现）

提供图形化的配置编辑界面，支持配置验证和保存。
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, List
import json

from common.log import logger
from config import conf, save_config, available_setting, load_config


class ConfigEditor:
    """
    配置编辑器（占位符实现）
    
    提供分类的配置编辑界面，支持配置验证和保存。
    """
    
    def __init__(self, parent_frame: ttk.Frame):
        """
        初始化配置编辑器
        
        Args:
            parent_frame: 父框架
        """
        self.parent_frame = parent_frame
        self.notebook = None
        self.config_vars = {}
        self.modified = False
        
        self._create_interface()
        self._load_config()
        
        logger.info("[ConfigEditor] 配置编辑器初始化完成（占位符）")
    
    def _create_interface(self):
        """创建配置编辑界面"""
        # 顶部按钮栏
        button_frame = ttk.Frame(self.parent_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 保存按钮
        save_button = ttk.Button(
            button_frame,
            text="保存配置",
            command=self._save_config,
            width=12
        )
        save_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 重置按钮
        reset_button = ttk.Button(
            button_frame,
            text="重置",
            command=self._reset_config,
            width=12
        )
        reset_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 导入按钮
        import_button = ttk.Button(
            button_frame,
            text="导入配置",
            command=self._import_config,
            width=12
        )
        import_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出按钮
        export_button = ttk.Button(
            button_frame,
            text="导出配置",
            command=self._export_config,
            width=12
        )
        export_button.pack(side=tk.LEFT)
        
        # 状态标签
        self.status_label = ttk.Label(
            button_frame,
            text="配置已加载",
            foreground="green"
        )
        self.status_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 配置选项卡
        self.notebook = ttk.Notebook(self.parent_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 创建配置分类选项卡
        self._create_basic_config_tab()
        self._create_message_config_tab()
        self._create_advanced_config_tab()
    
    def _create_basic_config_tab(self):
        """创建基础配置选项卡"""
        basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(basic_frame, text="基础配置")
        
        # 创建滚动框架
        canvas = tk.Canvas(basic_frame)
        scrollbar = ttk.Scrollbar(basic_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基础配置项
        basic_configs = [
            ("channel_type", "通道类型", "string", ["wx", "wxy", "terminal", "wechatmp", "wechatmp_service", "wechatcom_app", "wework", "dingtalk", "feishu", "gewechat", "web"]),
            ("model", "AI模型", "string", ["mock", "dify", "gpt-3.5-turbo", "gpt-4", "claude-3-sonnet", "wenxin", "moonshot", "qwen-turbo", "xunfei", "glm-4"]),
            ("debug", "调试模式", "boolean", None),
            ("dify_api_base", "Dify API地址", "string", None),
            ("dify_api_key", "Dify API密钥", "password", None),
            ("dify_app_type", "Dify应用类型", "string", ["chatbot", "agent", "workflow"]),
            ("open_ai_api_key", "OpenAI API密钥", "password", None),
            ("open_ai_api_base", "OpenAI API地址", "string", None),
        ]
        
        row = 0
        for key, label, input_type, options in basic_configs:
            self._create_config_item(scrollable_frame, key, label, input_type, options, row)
            row += 1
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def _create_message_config_tab(self):
        """创建消息配置选项卡"""
        message_frame = ttk.Frame(self.notebook)
        self.notebook.add(message_frame, text="消息配置")
        
        # 创建滚动框架
        canvas = tk.Canvas(message_frame)
        scrollbar = ttk.Scrollbar(message_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 消息配置项
        message_configs = [
            ("single_chat_prefix", "私聊触发前缀", "list", None),
            ("single_chat_reply_prefix", "私聊回复前缀", "string", None),
            ("group_chat_prefix", "群聊触发前缀", "list", None),
            ("group_name_white_list", "群聊白名单", "list", None),
            ("nick_name_black_list", "用户昵称黑名单", "list", None),
            ("auto_reply_text", "自动回复文本", "text", None),
            ("error_reply", "错误回复消息", "string", None),
            ("character_desc", "人格描述", "text", None),
            ("expires_in_seconds", "会话过期时间(秒)", "number", None),
        ]
        
        row = 0
        for key, label, input_type, options in message_configs:
            self._create_config_item(scrollable_frame, key, label, input_type, options, row)
            row += 1
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def _create_advanced_config_tab(self):
        """创建高级配置选项卡"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="高级配置")
        
        # 原始JSON编辑器
        json_frame = ttk.LabelFrame(advanced_frame, text="JSON配置编辑器", padding=10)
        json_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # JSON文本框
        self.json_text = tk.Text(
            json_frame,
            wrap=tk.WORD,
            font=("Consolas", 10),
            height=20
        )
        
        json_scrollbar = ttk.Scrollbar(json_frame, orient=tk.VERTICAL, command=self.json_text.yview)
        self.json_text.configure(yscrollcommand=json_scrollbar.set)
        
        self.json_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        json_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # JSON按钮
        json_button_frame = ttk.Frame(advanced_frame)
        json_button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(
            json_button_frame,
            text="格式化JSON",
            command=self._format_json
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            json_button_frame,
            text="验证JSON",
            command=self._validate_json
        ).pack(side=tk.LEFT)
    
    def _create_config_item(self, parent, key: str, label: str, input_type: str, options: list, row: int):
        """创建配置项控件"""
        # 标签
        ttk.Label(parent, text=f"{label}:").grid(row=row, column=0, sticky=tk.W, padx=(0, 10), pady=5)
        
        # 根据类型创建不同的输入控件
        if input_type == "boolean":
            var = tk.BooleanVar()
            widget = ttk.Checkbutton(parent, variable=var)
        elif input_type == "string" and options:
            var = tk.StringVar()
            widget = ttk.Combobox(parent, textvariable=var, values=options, state="readonly")
        elif input_type == "password":
            var = tk.StringVar()
            widget = ttk.Entry(parent, textvariable=var, show="*", width=30)
        elif input_type == "text":
            var = tk.StringVar()
            widget = tk.Text(parent, height=3, width=40)
        elif input_type == "list":
            var = tk.StringVar()
            widget = ttk.Entry(parent, textvariable=var, width=40)
            # 添加提示标签
            ttk.Label(parent, text="(用逗号分隔)", font=("Arial", 8), foreground="gray").grid(
                row=row, column=2, sticky=tk.W, padx=(5, 0)
            )
        elif input_type == "number":
            var = tk.StringVar()
            widget = ttk.Entry(parent, textvariable=var, width=30)
        else:
            var = tk.StringVar()
            widget = ttk.Entry(parent, textvariable=var, width=30)
        
        widget.grid(row=row, column=1, sticky=tk.W, pady=5)
        
        # 保存变量引用
        if input_type != "text":
            self.config_vars[key] = (var, input_type, options)
        else:
            self.config_vars[key] = (widget, input_type, options)
    
    def _load_config(self):
        """加载配置"""
        try:
            current_config = dict(conf())
            
            # 加载到表单控件
            for key, (var_or_widget, input_type, options) in self.config_vars.items():
                value = current_config.get(key, "")
                
                if input_type == "text":
                    var_or_widget.delete("1.0", tk.END)
                    var_or_widget.insert("1.0", str(value))
                elif input_type == "list":
                    if isinstance(value, list):
                        var_or_widget.set(", ".join(str(v) for v in value))
                    else:
                        var_or_widget.set(str(value))
                elif input_type == "boolean":
                    var_or_widget.set(bool(value))
                elif input_type == "number":
                    var_or_widget.set(str(value))
                else:
                    var_or_widget.set(str(value))
            
            # 加载到JSON编辑器
            if hasattr(self, 'json_text'):
                self.json_text.delete("1.0", tk.END)
                json_str = json.dumps(current_config, indent=2, ensure_ascii=False)
                self.json_text.insert("1.0", json_str)
            
            self.modified = False
            self._update_status("配置已加载", "green")
            
        except Exception as e:
            error_msg = f"加载配置失败: {str(e)}"
            logger.error(f"[ConfigEditor] {error_msg}")
            self._update_status(error_msg, "red")
    
    def _save_config(self):
        """保存配置"""
        try:
            # 验证配置
            validation_errors = self._validate_config()
            if validation_errors:
                error_msg = "配置验证失败:\n" + "\n".join(validation_errors)
                self._update_status("配置验证失败", "red")
                messagebox.showerror("验证错误", error_msg)
                return

            # 从表单控件收集配置
            new_config = {}
            for key, (var_or_widget, input_type, options) in self.config_vars.items():
                if input_type == "text":
                    value = var_or_widget.get("1.0", tk.END).strip()
                elif input_type == "list":
                    value_str = var_or_widget.get().strip()
                    if value_str:
                        value = [v.strip() for v in value_str.split(",") if v.strip()]
                    else:
                        value = []
                elif input_type == "boolean":
                    value = var_or_widget.get()
                elif input_type == "number":
                    value_str = var_or_widget.get().strip()
                    if value_str:
                        try:
                            value = int(value_str) if value_str.isdigit() else float(value_str)
                        except ValueError:
                            raise ValueError(f"配置项 {key} 必须是数字")
                    else:
                        value = ""
                else:
                    value = var_or_widget.get()
                
                if value != "":  # 只保存非空值
                    new_config[key] = value
            
            # 更新配置
            for key, value in new_config.items():
                conf().set(key, value)
            
            # 保存到文件
            save_config()
            
            self.modified = False
            self._update_status("配置已保存", "green")
            messagebox.showinfo("成功", "配置已保存成功！")
            
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            logger.error(f"[ConfigEditor] {error_msg}")
            self._update_status(error_msg, "red")
            messagebox.showerror("错误", error_msg)
    
    def _reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置所有配置吗？这将丢失当前的修改。"):
            self._load_config()
    
    def _import_config(self):
        """导入配置"""
        try:
            from tkinter import filedialog

            filename = filedialog.askopenfilename(
                title="导入配置文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)

                # 验证导入的配置
                valid_config = {}
                invalid_keys = []

                for key, value in imported_config.items():
                    if key in available_setting:
                        valid_config[key] = value
                    else:
                        invalid_keys.append(key)

                if invalid_keys:
                    messagebox.showwarning(
                        "导入警告",
                        f"以下配置项不在允许列表中，已忽略:\n{', '.join(invalid_keys)}"
                    )

                # 更新配置
                for key, value in valid_config.items():
                    conf().set(key, value)

                # 重新加载界面
                self._load_config()

                messagebox.showinfo("成功", f"配置已从 {filename} 导入成功！")
                logger.info(f"[ConfigEditor] 配置已从 {filename} 导入")

        except Exception as e:
            error_msg = f"导入配置失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            logger.error(f"[ConfigEditor] {error_msg}")

    def _export_config(self):
        """导出配置"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                title="导出配置文件",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filename:
                current_config = dict(conf())

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(current_config, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配置已导出到: {filename}")
                logger.info(f"[ConfigEditor] 配置已导出到: {filename}")

        except Exception as e:
            error_msg = f"导出配置失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            logger.error(f"[ConfigEditor] {error_msg}")
    
    def _format_json(self):
        """格式化JSON"""
        try:
            json_str = self.json_text.get("1.0", tk.END).strip()
            if json_str:
                data = json.loads(json_str)
                formatted = json.dumps(data, indent=2, ensure_ascii=False)
                self.json_text.delete("1.0", tk.END)
                self.json_text.insert("1.0", formatted)
                self._update_status("JSON格式化完成", "green")
        except Exception as e:
            error_msg = f"JSON格式化失败: {str(e)}"
            self._update_status(error_msg, "red")
            messagebox.showerror("错误", error_msg)
    
    def _validate_json(self):
        """验证JSON"""
        try:
            json_str = self.json_text.get("1.0", tk.END).strip()
            if json_str:
                json.loads(json_str)
                self._update_status("JSON格式正确", "green")
                messagebox.showinfo("验证结果", "JSON格式正确！")
            else:
                self._update_status("JSON内容为空", "orange")
        except Exception as e:
            error_msg = f"JSON格式错误: {str(e)}"
            self._update_status(error_msg, "red")
            messagebox.showerror("验证结果", error_msg)
    
    def _validate_config(self) -> List[str]:
        """
        验证配置

        Returns:
            List[str]: 验证错误列表
        """
        errors = []

        try:
            for key, (var_or_widget, input_type, options) in self.config_vars.items():
                # 获取值
                if input_type == "text":
                    value = var_or_widget.get("1.0", tk.END).strip()
                elif input_type == "list":
                    value_str = var_or_widget.get().strip()
                    if value_str:
                        value = [v.strip() for v in value_str.split(",") if v.strip()]
                    else:
                        value = []
                elif input_type == "boolean":
                    value = var_or_widget.get()
                elif input_type == "number":
                    value_str = var_or_widget.get().strip()
                    if value_str:
                        try:
                            value = int(value_str) if value_str.isdigit() else float(value_str)
                        except ValueError:
                            errors.append(f"配置项 '{key}' 必须是数字")
                            continue
                    else:
                        value = ""
                else:
                    value = var_or_widget.get()

                # 跳过空值
                if not value and value != 0 and value != False:
                    continue

                # 验证选项值
                if options and input_type == "string" and value not in options:
                    errors.append(f"配置项 '{key}' 的值 '{value}' 不在允许的选项中: {options}")

                # 特定配置项验证
                if key == "dify_api_key" and value and not value.startswith("app-"):
                    errors.append(f"Dify API密钥格式错误，应以 'app-' 开头")

                if key == "open_ai_api_key" and value and not value.startswith("sk-"):
                    errors.append(f"OpenAI API密钥格式错误，应以 'sk-' 开头")

                if key in ["dify_api_base", "open_ai_api_base"] and value:
                    if not (value.startswith("http://") or value.startswith("https://")):
                        errors.append(f"配置项 '{key}' 必须是有效的URL")

                if key == "expires_in_seconds" and isinstance(value, (int, float)) and value <= 0:
                    errors.append(f"会话过期时间必须大于0")

                # 检查是否在available_setting中
                if key not in available_setting:
                    errors.append(f"配置项 '{key}' 不在允许的配置列表中")

        except Exception as e:
            errors.append(f"验证过程中发生错误: {str(e)}")

        return errors

    def _update_status(self, message: str, color: str):
        """更新状态显示"""
        self.status_label.config(text=message, foreground=color)
    
    def cleanup(self):
        """清理资源"""
        logger.info("[ConfigEditor] 配置编辑器清理完成")
